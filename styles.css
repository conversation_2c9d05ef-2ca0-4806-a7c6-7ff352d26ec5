* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

header {
    background-color: #222;
    color: #fff;
    padding: 1rem;
    text-align: center;
}

main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#canvas-container {
    flex: 1;
    background-color: #e0e0e0;
    position: relative;
}

#main-canvas {
    width: 100%;
    height: 100%;
}

.bottom-panel {
    display: flex;
    background-color: #333;
}

#control-panel {
    flex: 2;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background-color: #333;
    color: #fff;
}

#control-panel button {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    background-color: #4CAF50;
    color: white;
    cursor: pointer;
}

#control-panel button:hover {
    background-color: #45a049;
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

#info-panel {
    flex: 1;
    padding: 1rem;
    background-color: #f5f5f5;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

select, input[type="range"] {
    padding: 0.3rem;
    border-radius: 4px;
} 