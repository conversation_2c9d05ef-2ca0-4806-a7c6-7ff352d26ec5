# 机场滑行模拟器

这是一个基于Vue 3的机场滑行模拟器项目，用于模拟飞机在机场跑道上的滑行过程。

## 功能特点

- 支持多种复杂度的跑道路径
- 真实的物理引擎模拟（加速、减速、转弯等）
- 平滑的动画效果
- 实时显示速度、距离等信息
- 可调节模拟速度
- 支持暂停/继续/重置功能

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── CanvasComponent.vue    # 画布渲染组件
│   ├── Controls.vue           # 控制面板组件
│   ├── InfoPanel.vue          # 信息显示面板
│   └── AirportSimulation.vue  # 主模拟组件
├── modules/             # 功能模块
│   ├── AircraftRenderer.js    # 飞机渲染器
│   ├── RunwayRenderer.js      # 跑道渲染器
│   ├── PathGeneration.js      # 路径生成模块
│   ├── PhysicsEngine.js       # 物理引擎
│   └── AnimationLoop.js       # 动画循环控制
├── config/              # 配置文件
│   ├── constants.js           # 项目常量
│   └── physics.js             # 物理引擎配置
└── App.vue             # 应用入口组件
```

## 技术栈

- Vue 3
- Canvas API
- JavaScript ES6+

## 开发说明

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run serve
```

### 构建生产版本
```bash
npm run build
```

## 核心模块说明

### 物理引擎
- 实现了基本的物理运动模拟
- 支持加速度、减速度的精确计算
- 转弯预判和速度自动调节

### 路径生成
- 支持多种预设跑道类型
- 可扩展的路径生成系统
- 平滑的路径插值计算

### 渲染系统
- 分离的渲染器架构
- 高性能Canvas渲染
- 流畅的动画效果

## 注意事项

1. 建议在Chrome或Firefox最新版本中运行
2. 画布大小会自动适应窗口尺寸
3. 性能优化已考虑移动设备

## 未来计划

- [ ] 添加更多跑道类型
- [ ] 支持自定义跑道路径
- [ ] 添加碰撞检测
- [ ] 支持多飞机同时运行
- [ ] 添加天气效果
