/**
 * 获取路径上指定距离处的位置和角度
 */
export function getPositionOnPath(points, distance) {
  let currentDist = 0;
  
  for (let i = 1; i < points.length; i++) {
    const segmentLength = calculateDistance(points[i-1], points[i]);
    
    if (currentDist + segmentLength >= distance) {
      const remainingDist = distance - currentDist;
      const ratio = remainingDist / segmentLength;
      
      const position = {
        x: points[i-1].x + (points[i].x - points[i-1].x) * ratio,
        y: points[i-1].y + (points[i].y - points[i-1].y) * ratio,
        angle: Math.atan2(
          points[i].y - points[i-1].y,
          points[i].x - points[i-1].x
        )
      };
      
      return position;
    }
    
    currentDist += segmentLength;
  }
  
  // 如果超出路径长度，返回终点位置和角度
  const lastPoint = points[points.length - 1];
  const secondLastPoint = points[points.length - 2];
  
  return {
    x: lastPoint.x,
    y: lastPoint.y,
    angle: Math.atan2(
      lastPoint.y - secondLastPoint.y,
      lastPoint.x - secondLastPoint.x
    )
  };
}

function calculateDistance(point1, point2) {
  return Math.hypot(point2.x - point1.x, point2.y - point1.y);
} 