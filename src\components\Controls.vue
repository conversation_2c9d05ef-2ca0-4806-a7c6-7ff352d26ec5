<template>
  <div class="controls">
    <div class="control-group">
      <button @click="$emit('start')" :disabled="isRunning">开始</button>
      <button @click="$emit('pause')" :disabled="!isRunning">暂停</button>
      <button @click="$emit('reset')">重置</button>
    </div>

    <div class="control-group">
      <label>模拟倍速：</label>
      <input
        type="range"
        :min="minSpeed"
        :max="maxSpeed"
        :step="speedStep"
        v-model="simulationRate"
        @input="updateSpeed"
      />
      <span>{{ simulationRate }}x</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ControlPanel',
  props: {
    speed: Number,
    isRunning: Boolean
  },
  data() {
    return {
      simulationRate: 1,    // 默认1倍速
      minSpeed: 0.1,        // 最小0.1倍速
      maxSpeed: 5,          // 最大5倍速
      speedStep: 0.1        // 步进0.1
    }
  },
  methods: {
    updateSpeed() {
      this.$emit('speed-change', Number(this.simulationRate))
    }
  },
  watch: {
    speed(newSpeed) {
      this.simulationRate = newSpeed
    }
  }
}
</script>

<style scoped>
.controls {
  background-color: #333;
  padding: 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  background-color: #4CAF50;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: #45a049;
}

select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #ddd;
}

input[type="range"] {
  width: 150px;
}

label {
  color: white;
  margin-right: 0.5rem;
}

span {
  color: white;
  min-width: 60px;
}
</style>
