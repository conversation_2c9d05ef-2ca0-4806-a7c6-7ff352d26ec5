<template>
  <div class="airport-simulator">
    <header>
      <h1>机场滑行模拟器</h1>
    </header>

    <main>
      <canvas-component
          ref="canvas"
          :width="canvasWidth"
          :height="canvasHeight"
          :isRunning="isSimulationRunning"
          :simulationSpeed="simulationSpeed"
          :simulationTime="simulationTime"
          @update:speed="updateCurrentSpeed"
          @update:distance="updateDistance"
          @simulation-complete="pauseSimulation"
      />

      <div class="bottom-panel">
        <controls
            :speed="simulationSpeed"
            :isRunning="isSimulationRunning"
            @speed-change="updateSpeed"
            @start="startSimulation"
            @pause="pauseSimulation"
            @reset="resetSimulation"
        />

        <info-panel
            :simulationTime="simulationTime"
            :currentSpeed="currentSpeed"
            :travelDistance="travelDistance"
        />
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import CanvasComponent from './components/CanvasComponent.vue'
import Controls from './components/Controls.vue'
import InfoPanel from './components/InfoPanel.vue'

export default {
  name: 'App',
  components: {
    CanvasComponent,
    Controls,
    InfoPanel
  },

  setup() {
    const canvas = ref(null)
    const canvasWidth = ref(window.innerWidth)
    const canvasHeight = ref(window.innerHeight - 200) // 减去头部和底部面板的高度
    
    const simulationSpeed = ref(1) // 模拟倍速
    const isSimulationRunning = ref(false)
    const simulationTime = ref(0)
    let simulationInterval = null
    let lastUpdateTime = 0
    
    const currentSpeed = ref(0)
    const travelDistance = ref(0)
    
    // 监听窗口大小变化
    const handleResize = () => {
      canvasWidth.value = window.innerWidth
      canvasHeight.value = window.innerHeight - 200
    }
    
    // 初始化事件监听
    onMounted(() => {
      window.addEventListener('resize', handleResize)
    })
    
    // 清理事件监听
    onUnmounted(() => {
      window.removeEventListener('resize', handleResize)
      if (simulationInterval) {
        clearInterval(simulationInterval)
      }
    })
    
    // 更新模拟速度
    const updateSpeed = (speed) => {
      simulationSpeed.value = speed
      
      // 更新模拟速度时重启时间计时器，以应用新的速度
      if (isSimulationRunning.value) {
        pauseSimulation()
        startSimulation()
      }
    }
    
    // 更新当前速度
    const updateCurrentSpeed = (speed) => {
      currentSpeed.value = speed
    }
    
    // 更新行驶距离
    const updateDistance = (distance) => {
      travelDistance.value = distance
    }
    
    // 开始模拟
    const startSimulation = () => {
      if (isSimulationRunning.value) return
      
      isSimulationRunning.value = true
      
      // 使用更精确的时间更新方法，考虑模拟倍速
      lastUpdateTime = Date.now()
      
      simulationInterval = setInterval(() => {
        const now = Date.now()
        const realTimeDelta = now - lastUpdateTime // 实际经过的时间（毫秒）
        
        // 根据模拟倍速调整时间增量
        const simulatedTimeDelta = realTimeDelta * simulationSpeed.value
        
        // 更新模拟时间
        simulationTime.value += simulatedTimeDelta
        
        // 更新上次更新时间
        lastUpdateTime = now
      }, 16) // 约60fps的更新频率
    }
    
    // 暂停模拟
    const pauseSimulation = () => {
      isSimulationRunning.value = false
      if (simulationInterval) {
        clearInterval(simulationInterval)
        simulationInterval = null
      }
    }
    
    // 重置模拟
    const resetSimulation = () => {
      pauseSimulation()
      simulationTime.value = 0
      currentSpeed.value = 0
      travelDistance.value = 0
      
      if (canvas.value) {
        canvas.value.resetSimulation()
      }
    }
    
    return {
      canvas,
      canvasWidth,
      canvasHeight,
      simulationSpeed,
      isSimulationRunning,
      simulationTime,
      currentSpeed,
      travelDistance,
      updateSpeed,
      startSimulation,
      pauseSimulation,
      resetSimulation,
      updateDistance,
      updateCurrentSpeed,
    }
  }
}
</script>

<style>
.airport-simulator {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

header {
  background-color: #222;
  color: #fff;
  padding: 0.2rem;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

header h1 {
  font-size: 1.5rem;
  font-weight: normal;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #e0e0e0;
  position: relative;
}

.bottom-panel {
  display: flex;
  background-color: #333;
}
</style>