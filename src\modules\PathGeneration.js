import { AirportGraphLoader } from '@/modules/AirportGraphLoader';

// 机场图数据加载器实例
const airportGraph = new AirportGraphLoader();

/**
 * 生成可用的路径
 * @returns {Array} 可用路径数组
 */
export function generateRunwayPaths() {
  // 获取所有可用的起点和终点
  const { runways, gates } = airportGraph.getRunwaysAndGates();
  
  // 生成所有可能的起点到终点组合
  const paths = [];
  
  runways.forEach(runway => {
    gates.forEach(gate => {
      // 查找从跑道到登机口的路径
      const path = airportGraph.findShortestPath(runway.id, gate.id);
      if (path.length > 0) {
        // 将路径转换为画布坐标
        const transformedPath = path.map(node => ({
          x: node.x, // 将在渲染时转换
          y: node.y,
          id: node.id,
          specification: node.specification
        }));
        
        paths.push(transformedPath);
      }
    });
  });
  
  return paths;
}

/**
 * 从多条可能路径中选择一条
 * @param {Array} paths - 所有可能的路径
 * @param {Object} options - 选择选项
 * @returns {Array} 选中的路径
 */
export function selectPath(paths, options = {}) {
  if (!paths || paths.length === 0) return [];
  
  // 可以根据不同策略选择路径
  if (options.preferShortest) {
    // 选择最短路径
    return paths.reduce((shortest, current) => {
      const currentLength = calculatePathLength(current);
      const shortestLength = calculatePathLength(shortest);
      return currentLength < shortestLength ? current : shortest;
    }, paths[0]);
  }
  
  // 默认随机选择一条路径
  return paths[Math.floor(Math.random() * paths.length)];
}

/**
 * 计算路径总长度
 * @param {Array} points - 路径点数组
 * @returns {number} 路径总长度
 */
export function calculatePathLength(points) {
  return points.reduce((length, point, index) => {
    if (index === 0) return 0;
    return length + calculateDistance(points[index - 1], point);
  }, 0);
}

/**
 * 计算两点之间的距离
 * @param {Object} point1 - 第一个点
 * @param {Object} point2 - 第二个点
 * @returns {number} 两点之间的距离
 */
function calculateDistance(point1, point2) {
  return Math.hypot(point2.x - point1.x, point2.y - point1.y);
}

/**
 * 获取路径上指定距离处的位置和角度
 * @param {Array} points - 路径点数组
 * @param {number} distance - 目标距离
 * @returns {Object} 包含位置和角度的对象
 */
export function getPositionOnPath(points, distance) {
  let currentDist = 0;
  
  for (let i = 1; i < points.length; i++) {
    const segmentLength = calculateDistance(points[i-1], points[i]);
    
    if (currentDist + segmentLength >= distance) {
      const remainingDist = distance - currentDist;
      const ratio = remainingDist / segmentLength;
      
      const position = {
        x: points[i-1].x + (points[i].x - points[i-1].x) * ratio,
        y: points[i-1].y + (points[i].y - points[i-1].y) * ratio,
        angle: Math.atan2(
          points[i].y - points[i-1].y,
          points[i].x - points[i-1].x
        )
      };
      
      return position;
    }
    
    currentDist += segmentLength;
  }
  
  // 如果超出路径长度，返回终点位置和角度
  const lastPoint = points[points.length - 1];
  const secondLastPoint = points[points.length - 2];
  
  return {
    x: lastPoint.x,
    y: lastPoint.y,
    angle: Math.atan2(
      lastPoint.y - secondLastPoint.y,
      lastPoint.x - secondLastPoint.x
    )
  };
}

/**
 * 获取机场图数据加载器实例
 * @returns {AirportGraphLoader} 机场图数据加载器实例
 */
export function getAirportGraph() {
  return airportGraph;
} 