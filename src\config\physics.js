/**
 * 物理引擎相关配置
 */
export const PhysicsConfig = {
  // 速度相关配置
  SPEED: {
    MIN: 0,     // 最小速度（千米/时）
    MAX: 20,    // 最大速度（千米/时）
    TURN: 10    // 转弯速度（千米/时）
  },

  // 加速度相关配置
  ACCELERATION: {
    NORMAL: 5,  // 正常加速度（千米/时/秒）
    BRAKE: 10,  // 制动减速度（千米/时/秒）
    TURN: 2     // 转弯加速度（千米/时/秒）
  },

  // 距离相关配置
  DISTANCE: {
    TURN_PREDICTION: 20, // 转弯预测距离（米）
    NODE_RADIUS: 15      // 节点判定半径（米）
  },

  // 角度相关配置
  ANGLE: {
    TURN_THRESHOLD: 0.2  // 转弯阈值（弧度）
  },

  // 转换系数
  CONVERSION: {
    KMH_TO_MS: 1/3.6,    // 千米/时 转 米/秒
    MS_TO_KMH: 3.6,      // 米/秒 转 千米/时
    DEG_TO_RAD: Math.PI / 180,  // 角度转弧度
    RAD_TO_DEG: 180 / Math.PI   // 弧度转角度
  }
}; 