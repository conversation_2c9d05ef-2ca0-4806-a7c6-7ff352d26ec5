<template>
  <div class="canvas-container">
    <canvas ref="canvas" :width="width" :height="height"></canvas>
  </div>
</template>

<script>
import { ref, onMounted, watch, onUnmounted } from 'vue'
import { AirportGraphLoader } from '@/modules/AirportGraphLoader';
import { AircraftRenderer } from '@/modules/AircraftRenderer'
import { RunwayRenderer } from '@/modules/RunwayRenderer'
import { AircraftManager } from '@/modules/AircraftManager'

export default {
  name: 'CanvasComponent',
  props: {
    width: Number,
    height: Number,
    isRunning: Boolean,
    simulationSpeed: Number,
    simulationTime: Number,
  },

  setup(props, {emit}) {
    const canvas = ref(null)
    let ctx = null
    let aircraftRenderer = null
    let runwayRenderer = null
    let aircraftManager = null
    let airportGraph = null
    let transformedNodes = null

    // 视图变换相关状态
    let scale = 1
    let offsetX = 0
    let offsetY = 0
    let isDragging = false
    let lastMouseX = 0
    let lastMouseY = 0

    // 飞机状态统计
    const activeAircraftCount = ref(0)
    const completedAircraftCount = ref(0)
    const totalAircraftCount = ref(0)

    // 初始化画布
    const initCanvas = () => {
      const canvasEl = canvas.value
      if (!canvasEl) return

      ctx = canvasEl.getContext('2d')
      
      // 初始化渲染器
      aircraftRenderer = new AircraftRenderer(ctx)
      runwayRenderer = new RunwayRenderer(ctx)
      
      // 初始化飞机管理器
      aircraftManager = new AircraftManager()
      airportGraph = new AirportGraphLoader()
      
      // 设置初始飞机数量
      totalAircraftCount.value = aircraftManager.aircrafts.length
      
      // 转换节点坐标
      transformedNodes = airportGraph.transformCoordinates(
        airportGraph.nodes, 
        props.width, 
        props.height, 
        50
      )
      
      // 为所有飞机生成路径
      aircraftManager.generatePaths(transformedNodes)
      
      // 开始渲染循环
      requestAnimationFrame(render)
      
      // 添加鼠标事件监听
      setupMouseInteractions()
    }

    // 设置鼠标交互
    const setupMouseInteractions = () => {
      const canvasEl = canvas.value
      if (!canvasEl) return
      
      canvasEl.addEventListener('mousedown', handleMouseDown)
      canvasEl.addEventListener('mousemove', handleMouseMove)
      canvasEl.addEventListener('mouseup', handleMouseUp)
      canvasEl.addEventListener('wheel', handleWheel)
    }

    // 处理鼠标按下事件
    const handleMouseDown = (event) => {
      isDragging = true
      lastMouseX = event.offsetX
      lastMouseY = event.offsetY
    }

    // 处理鼠标移动事件
    const handleMouseMove = (event) => {
      if (!isDragging) return
      
      const deltaX = event.offsetX - lastMouseX
      const deltaY = event.offsetY - lastMouseY
      
      offsetX += deltaX
      offsetY += deltaY
      
      lastMouseX = event.offsetX
      lastMouseY = event.offsetY
    }

    // 处理鼠标释放事件
    const handleMouseUp = () => {
      isDragging = false
    }

    // 处理鼠标滚轮事件（缩放）
    const handleWheel = (event) => {
      event.preventDefault()
      
      const zoomIntensity = 0.1
      const delta = event.deltaY < 0 ? zoomIntensity : -zoomIntensity
      const newScale = scale * (1 + delta)
      
      // 限制缩放范围
      if (newScale < 0.2 || newScale > 5) return
      
      // 更新缩放比例
      scale = newScale
    }

    // 应用视图变换
    const applyTransform = () => {
      ctx.save()
      ctx.translate(offsetX, offsetY)
      ctx.scale(scale, scale)
    }

    // 重置视图变换
    const resetTransform = () => {
      ctx.restore()
    }

    // 重置模拟
    const resetSimulation = () => {
      if (aircraftManager) {
        aircraftManager.resetAll()
      }
      
      activeAircraftCount.value = 0
      completedAircraftCount.value = 0
    }

    // 渲染循环
    const render = () => {
      if (!ctx) return
      
      ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height)
      
      // 绘制调试信息
      ctx.save()
      ctx.fillStyle = '#333'
      ctx.font = '14px Arial'
      ctx.fillText(`活跃飞机: ${activeAircraftCount.value}/${totalAircraftCount.value}`, 10, 20)
      ctx.fillText(`已完成: ${completedAircraftCount.value}/${totalAircraftCount.value}`, 10, 40)
      ctx.fillText(`模拟时间: ${formatTime(props.simulationTime)}`, 10, 60)
      ctx.fillText(`模拟倍速: ${props.simulationSpeed}x`, 10, 80)
      ctx.restore()
      
      applyTransform()
      
      // 渲染机场地图
      if (airportGraph && transformedNodes) {
        runwayRenderer.renderAirportMap(transformedNodes, airportGraph.edges);
      }
      
      // 更新飞机状态
      if (props.isRunning && aircraftManager) {
        const stats = aircraftManager.updateAircrafts(props.simulationTime, props.simulationSpeed);
        
        activeAircraftCount.value = stats.activeCount;
        completedAircraftCount.value = stats.completedCount;
        
        // 更新UI信息
        emit('update:speed', aircraftManager.getAverageSpeed());
        emit('update:distance', aircraftManager.getTotalTravelDistance());
        
        // 检查是否所有飞机都完成了
        if (stats.completedCount === stats.totalCount) {
          emit('simulation-complete');
        }
      }
      
      // 渲染飞机
      if (aircraftManager) {
        const aircraftPositions = aircraftManager.getAircraftPositions();
        aircraftRenderer.render(aircraftPositions);
      }
      
      resetTransform()
      
      requestAnimationFrame(render)
    }
    
    // 格式化时间为 HH:MM:SS
    const formatTime = (ms) => {
      const seconds = Math.floor(ms / 1000)
      const minutes = Math.floor(seconds / 60)
      const hours = Math.floor(minutes / 60)
      return `${hours.toString().padStart(2, '0')}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`
    }

    // 组件挂载时初始化
    onMounted(() => {
      initCanvas()
    })

    // 监听属性变化
    watch(() => [props.width, props.height], () => {
      if (ctx) {
        ctx.canvas.width = props.width
        ctx.canvas.height = props.height
        
        // 重新转换坐标
        if (airportGraph) {
          transformedNodes = airportGraph.transformCoordinates(
            airportGraph.nodes, 
            props.width, 
            props.height, 
            50
          )
          
          // 为所有飞机重新生成路径
          if (aircraftManager) {
            aircraftManager.generatePaths(transformedNodes)
          }
        }
      }
    })

    // 组件卸载时清理
    onUnmounted(() => {
      const canvasEl = canvas.value
      if (canvasEl) {
        canvasEl.removeEventListener('mousedown', handleMouseDown)
        canvasEl.removeEventListener('mousemove', handleMouseMove)
        canvasEl.removeEventListener('mouseup', handleMouseUp)
        canvasEl.removeEventListener('wheel', handleWheel)
      }
    })

    return {
      canvas,
      resetSimulation,
      activeAircraftCount,
      completedAircraftCount,
      totalAircraftCount
    }
  }
}
</script>

<style scoped>
.canvas-container {
  flex: 1;
  background-color: #f0f0f0;
  position: relative;
}

canvas {
  width: 100%;
  height: 100%;
}
</style>
