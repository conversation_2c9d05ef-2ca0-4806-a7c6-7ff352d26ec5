import aircraftData from '@/assets/aircraft.json';
import { getPositionOnPath } from '@/modules/PathUtils';
import { PhysicsConfig } from '@/config/physics';

export class AircraftManager {
  constructor() {
    this.aircrafts = [];
    this.loadAircrafts();
  }

  // 加载飞机数据
  loadAircrafts() {
    console.log("Loading aircraft data:", aircraftData.length, "aircrafts");
    
    aircraftData.forEach((aircraft, index) => {
      // 解析起始时间
      const startTimeStr = aircraft.Start_Time || aircraft['Start Time'];
      const startTime = this.parseTimeToMilliseconds(startTimeStr);
      
      // 创建飞机对象
      this.aircrafts.push({
        id: `aircraft-${index}`,
        type: aircraft.Type,
        startNode: aircraft.Start_Node || aircraft['Start Node'],
        endNode: aircraft.End_Node || aircraft['End Node'],
        route: aircraft.route,
        startTime: startTime,
        weightClass: aircraft.Weight_Class || aircraft['Weight Class'],
        isActive: false,
        currentSpeed: 0,
        targetSpeed: PhysicsConfig.SPEED.MAX,
        currentDistance: 0,
        path: [],
        pathLength: 0,
        completed: false
      });
    });
    
    console.log("Loaded", this.aircrafts.length, "aircraft");
  }

  // 将时间字符串解析为毫秒数（从模拟开始）
  parseTimeToMilliseconds(timeStr) {
    if (!timeStr) return 0;
    
    const parts = timeStr.split(':');
    if (parts.length !== 3) return 0;
    
    const hours = parseInt(parts[0]);
    const minutes = parseInt(parts[1]);
    const seconds = parseInt(parts[2]);
    
    return ((hours * 60 + minutes) * 60 + seconds) * 1000;
  }

  // 为每架飞机生成路径（转换坐标）
  generatePaths(transformedNodes) {
    this.aircrafts.forEach(aircraft => {
      if (aircraft.path.length > 0) return;
      
      // 使用预定义的路由转换为画布坐标
      const path = aircraft.route.map(nodeId => {
        const transformedNode = transformedNodes[nodeId];
        if (!transformedNode) {
          console.warn(`Node ${nodeId} not found in transformed nodes`);
          return null;
        }
        
        return {
          x: transformedNode.canvasX,
          y: transformedNode.canvasY,
          id: nodeId
        };
      }).filter(node => node !== null);
      
      aircraft.path = path;
      aircraft.pathLength = this.calculatePathLength(path);
    });
  }

  // 计算路径长度
  calculatePathLength(path) {
    return path.reduce((length, point, index) => {
      if (index === 0) return 0;
      return length + this.calculateDistance(path[index - 1], point);
    }, 0);
  }

  // 计算两点间距离
  calculateDistance(point1, point2) {
    return Math.hypot(point2.x - point1.x, point2.y - point1.y);
  }

  // 更新所有活跃飞机的状态
  updateAircrafts(simulationTime, simulationRate) {
    const deltaTime = 16.67; // ms (约60fps)
    const dt = (deltaTime / 1000) * simulationRate; // 考虑模拟倍速
    
    // 检查是否有新飞机需要激活
    this.aircrafts.forEach(aircraft => {
      if (!aircraft.isActive && !aircraft.completed && simulationTime >= aircraft.startTime) {
        console.log(`Activating aircraft ${aircraft.id} at time ${simulationTime}ms`);
        aircraft.isActive = true;
      }
    });
    
    // 更新活跃飞机
    this.aircrafts.forEach(aircraft => {
      if (!aircraft.isActive || aircraft.completed) return;
      
      // 检查前方是否有转弯
      const turningAhead = this.checkTurnAhead(aircraft);
      
      // 根据情况调整目标速度
      let targetSpeed = PhysicsConfig.SPEED.MAX;
      if (turningAhead) {
        targetSpeed = PhysicsConfig.SPEED.TURN;
      }
      
      // 检查是否需要刹车
      const distanceToEnd = aircraft.pathLength - aircraft.currentDistance;
      const brakingDistance = this.calculateBrakingDistance(aircraft.currentSpeed);
      
      if (distanceToEnd <= brakingDistance) {
        // 需要减速停止
        const speedMS = aircraft.currentSpeed * PhysicsConfig.CONVERSION.KMH_TO_MS;
        const decelMS = PhysicsConfig.ACCELERATION.BRAKE * PhysicsConfig.CONVERSION.KMH_TO_MS;
        const newSpeedMS = Math.sqrt(Math.max(0, 
          speedMS * speedMS - 2 * decelMS * (dt * speedMS)
        ));
        aircraft.currentSpeed = newSpeedMS * PhysicsConfig.CONVERSION.MS_TO_KMH;
      } 
      else if (aircraft.currentSpeed > targetSpeed) {
        // 减速
        aircraft.currentSpeed = Math.max(targetSpeed, 
          aircraft.currentSpeed - PhysicsConfig.ACCELERATION.BRAKE * dt);
      } 
      else {
        // 加速
        aircraft.currentSpeed = Math.min(targetSpeed, 
          aircraft.currentSpeed + PhysicsConfig.ACCELERATION.NORMAL * dt);
      }
      
      // 更新距离
      const distanceDelta = aircraft.currentSpeed * PhysicsConfig.CONVERSION.KMH_TO_MS * dt;
      aircraft.currentDistance = Math.min(aircraft.pathLength, aircraft.currentDistance + distanceDelta);
      
      // 检查是否到达终点
      if (aircraft.currentDistance >= aircraft.pathLength) {
        if (aircraft.currentSpeed < 1) {
          aircraft.completed = true;
          console.log(`Aircraft ${aircraft.id} completed its path`);
        }
      }
    });
    
    return {
      activeCount: this.aircrafts.filter(a => a.isActive && !a.completed).length,
      completedCount: this.aircrafts.filter(a => a.completed).length,
      totalCount: this.aircrafts.length
    };
  }
  
  // 获取飞机在路径上的位置
  getAircraftPositions() {
    return this.aircrafts
      .filter(aircraft => aircraft.isActive && !aircraft.completed)
      .map(aircraft => {
        const position = getPositionOnPath(aircraft.path, aircraft.currentDistance);
        return {
          id: aircraft.id,
          position: position,
          type: aircraft.type,
          weightClass: aircraft.weightClass
        };
      });
  }
  
  // 检查前方是否有转弯
  checkTurnAhead(aircraft) {
    if (!aircraft.path || aircraft.path.length < 2) return false;
    
    const lookAheadDistance = PhysicsConfig.DISTANCE.TURN_PREDICTION;
    const currentPos = getPositionOnPath(aircraft.path, aircraft.currentDistance);
    const aheadPos = getPositionOnPath(aircraft.path, aircraft.currentDistance + lookAheadDistance);
    
    if (!currentPos || !aheadPos) return false;
    
    const angle = Math.abs(currentPos.angle - aheadPos.angle);
    return angle > PhysicsConfig.ANGLE.TURN_THRESHOLD;
  }
  
  // 计算制动距离
  calculateBrakingDistance(speed) {
    const speedMS = speed * PhysicsConfig.CONVERSION.KMH_TO_MS;
    return (speedMS * speedMS) / (2 * (PhysicsConfig.ACCELERATION.BRAKE * PhysicsConfig.CONVERSION.KMH_TO_MS));
  }
  
  // 获取活跃飞机的平均速度
  getAverageSpeed() {
    const activeAircrafts = this.aircrafts.filter(a => a.isActive && !a.completed);
    if (activeAircrafts.length === 0) return 0;
    
    const totalSpeed = activeAircrafts.reduce((sum, aircraft) => sum + aircraft.currentSpeed, 0);
    return totalSpeed / activeAircrafts.length;
  }
  
  // 获取所有飞机的总行驶距离
  getTotalTravelDistance() {
    return this.aircrafts.reduce((sum, aircraft) => sum + aircraft.currentDistance, 0);
  }
  
  // 重置所有飞机状态
  resetAll() {
    this.aircrafts.forEach(aircraft => {
      aircraft.isActive = false;
      aircraft.currentSpeed = 0;
      aircraft.currentDistance = 0;
      aircraft.completed = false;
    });
  }
} 