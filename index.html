<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>机场滑行模拟器</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>机场滑行模拟器</h1>
        </header>
        
        <main>
            <div id="canvas-container">
                <canvas id="main-canvas"></canvas>
            </div>
            
            <div class="bottom-panel">
                <div id="control-panel">
                    <button id="start-btn">开始</button>
                    <button id="pause-btn">暂停</button>
                    <button id="reset-btn">重置</button>
                    
                    <div class="speed-control">
                        <label for="speed-slider">速度控制:</label>
                        <input type="range" id="speed-slider" min="0.5" max="2" step="0.5" value="1">
                        <span id="speed-value">1x</span>
                    </div>
                    
                    <select id="runway-select">
                        <option value="simple">简单跑道</option>
                        <option value="medium">中等跑道</option>
                        <option value="complex">复杂跑道</option>
                    </select>
                </div>
                
                <div id="info-panel">
                    <div class="info-item">
                        <span>模拟时间:</span>
                        <span id="simulation-time">00:00:00</span>
                    </div>
                    <div class="info-item">
                        <span>当前速度:</span>
                        <span id="current-speed">0 km/h</span>
                    </div>
                    <div class="info-item">
                        <span>滑行距离:</span>
                        <span id="travel-distance">0 m</span>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html> 