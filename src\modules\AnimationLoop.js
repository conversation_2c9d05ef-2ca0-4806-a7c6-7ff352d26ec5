import Matter from 'matter-js'

export function startAnimationLoop(engine, updateCallback) {
  let lastTime = 0
  
  function animate(currentTime) {
    const deltaTime = currentTime - lastTime
    lastTime = currentTime
    
    // 更新物理引擎
    Matter.Engine.update(engine, deltaTime)
    
    // 调用更新回调
    if (updateCallback) {
      updateCallback(deltaTime)
    }
    
    return requestAnimationFrame(animate)
  }
  
  return requestAnimationFrame(animate)
} 