import Matter from 'matter-js'
import { PhysicsConfig } from '@/config/physics'

export function initPhysicsEngine() {
  const engine = Matter.Engine.create({
    enableSleeping: false,
    constraintIterations: 4,
    velocityIterations: 8,
    positionIterations: 6
  })
  
  // 配置物理引擎参数
  engine.world.gravity.y = 0 // 禁用重力
  engine.timing.timeScale = 1 // 时间缩放因子
  
  return engine
}

export function createAircraft(x, y) {
  return Matter.Bodies.rectangle(x, y, 50, 30, {
    label: 'aircraft',
    friction: 0.1,
    frictionAir: 0.01,
    restitution: 0.1,
    density: 0.001,
    chamfer: { radius: 5 },
    render: {
      fillStyle: '#4a90e2'
    }
  })
}

export function createRunway(points) {
  // 创建跑道的物理边界
  return points.map((point, index) => {
    if (index === points.length - 1) return null
    
    const nextPoint = points[index + 1]
    const dx = nextPoint.x - point.x
    const dy = nextPoint.y - point.y
    const length = Math.hypot(dx, dy)
    const angle = Math.atan2(dy, dx)
    
    return Matter.Bodies.rectangle(
      (point.x + nextPoint.x) / 2,
      (point.y + nextPoint.y) / 2,
      length,
      10,
      {
        isStatic: true,
        angle: angle,
        friction: 0.2,
        render: {
          fillStyle: '#666666'
        }
      }
    )
  }).filter(Boolean)
}

export function updateAircraftPhysics(aircraft, targetSpeed, deltaTime) {
  const speed = Matter.Vector.magnitude(aircraft.velocity)
  const currentAngle = aircraft.angle
  const targetAngle = Math.atan2(aircraft.velocity.y, aircraft.velocity.x)
  
  // 速度调整
  const speedDiff = targetSpeed - speed
  const acceleration = speedDiff > 0 ? 
    PhysicsConfig.ACCELERATION.NORMAL : 
    PhysicsConfig.ACCELERATION.BRAKE
  
  const force = Math.min(Math.abs(speedDiff), acceleration * deltaTime)
  const forceAngle = speedDiff > 0 ? currentAngle : (currentAngle + Math.PI)
  
  Matter.Body.applyForce(aircraft, aircraft.position, {
    x: force * Math.cos(forceAngle),
    y: force * Math.sin(forceAngle)
  })
  
  // 角度调整
  const angleDiff = targetAngle - currentAngle
  const turnRate = Math.min(
    Math.abs(angleDiff),
    PhysicsConfig.ANGLE.MAX_TURN_RATE * deltaTime
  )
  
  Matter.Body.rotate(aircraft, 
    Math.sign(angleDiff) * turnRate
  )
} 