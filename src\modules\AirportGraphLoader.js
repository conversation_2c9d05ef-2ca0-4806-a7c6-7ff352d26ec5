import graphData from '@/assets/graph.json';

export class AirportGraphLoader {
  constructor() {
    this.nodes = {};
    this.edges = {};
    this.loadGraph();
  }

  loadGraph() {
    console.log("Raw graph data:", graphData);
    
    if (!graphData) {
      console.error("Graph data is undefined");
      return;
    }
    
    // 检查数据结构
    if (!graphData.nodes || !Array.isArray(graphData.nodes)) {
      console.error("Invalid nodes data structure:", graphData.nodes);
      if (graphData.graph && graphData.graph.nodes) {
        console.log("Using alternative nodes data from graph.nodes");
        this.processNodes(graphData.graph.nodes);
      } else {
        console.error("No valid node data found");
        return;
      }
    } else {
      console.log("Processing", graphData.nodes.length, "nodes");
      this.processNodes(graphData.nodes);
    }
    
    // 处理边
    if (!graphData.edges || !Array.isArray(graphData.edges)) {
      console.error("Invalid edges data structure:", graphData.edges);
      if (graphData.graph && graphData.graph.edges) {
        console.log("Using alternative edges data from graph.edges");
        this.processEdges(graphData.graph.edges);
      } else if (graphData.links) {
        console.log("Using links as edges");
        this.processEdges(graphData.links);
      }
    } else {
      console.log("Processing", graphData.edges.length, "edges");
      this.processEdges(graphData.edges);
    }
  }
  
  processNodes(nodes) {
    nodes.forEach(node => {
      if (!node || !node.id) {
        console.warn("Skipping invalid node:", node);
        return;
      }
      
      // 处理可能不同的坐标格式
      let x, y;
      if (Array.isArray(node.pos) && node.pos.length >= 2) {
        x = node.pos[0];
        y = node.pos[1];
      } else if (node.x !== undefined && node.y !== undefined) {
        x = node.x;
        y = node.y;
      } else {
        console.warn("Node missing position data:", node);
        return;
      }
      
      this.nodes[node.id] = {
        id: node.id,
        x: x,
        y: y,
        specification: node.specification
      };
    });
  }
  
  processEdges(edges) {
    edges.forEach(edge => {
      if (!edge || !edge.source || !edge.target) {
        console.warn("Skipping invalid edge:", edge);
        return;
      }
      
      const edgeId = `${edge.source}-${edge.target}`;
      this.edges[edgeId] = {
        source: edge.source,
        target: edge.target,
        length: edge.length || 1
      };
    });
  }
  
  // 坐标转换 (经纬度到画布坐标)
  transformCoordinates(nodes, canvasWidth, canvasHeight, padding = 50) {
    // 找出经纬度的最小和最大值
    let minLat = Infinity, maxLat = -Infinity;
    let minLng = Infinity, maxLng = -Infinity;
    
    Object.values(nodes).forEach(node => {
      minLat = Math.min(minLat, node.y);
      maxLat = Math.max(maxLat, node.y);
      minLng = Math.min(minLng, node.x);
      maxLng = Math.max(maxLng, node.x);
    });
    
    console.log("Coordinate ranges:", {minLng, maxLng, minLat, maxLat});
    
    // 检查是否有有效的范围
    if (minLat === maxLat || minLng === maxLng) {
      console.warn("Invalid coordinate range");
      if (minLat === maxLat) {
        minLat -= 0.001;
        maxLat += 0.001;
      }
      if (minLng === maxLng) {
        minLng -= 0.001;
        maxLng += 0.001;
      }
    }
    
    // 计算缩放系数
    const latRange = maxLat - minLat;
    const lngRange = maxLng - minLng;
    
    const availableWidth = canvasWidth - 2 * padding;
    const availableHeight = canvasHeight - 2 * padding;
    
    const scaleX = availableWidth / lngRange;
    const scaleY = availableHeight / latRange;
    
    // 使用较小的缩放系数保持比例
    const scale = Math.min(scaleX, scaleY);
    
    console.log("Transform scale:", scale, "Canvas size:", canvasWidth, canvasHeight);
    
    // 转换所有坐标
    const transformedNodes = {};
    
    Object.entries(nodes).forEach(([id, node]) => {
      transformedNodes[id] = {
        ...node,
        canvasX: padding + (node.x - minLng) * scale,
        canvasY: canvasHeight - padding - (node.y - minLat) * scale // 翻转Y轴
      };
    });
    
    return transformedNodes;
  }
} 