[{"Start Node": "A10", "End Node": "N8", "Start Time": "0:0:00", "Weight Class": "2", "route": ["A10", "N407", "N163", "N161", "N205", "N144", "N206", "N143", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N210", "N135", "N211", "N133", "N212", "N131", "N184", "N228", "N227", "N226", "N225", "N224", "N223", "N222", "N221", "N220", "N219", "N218", "N217", "N216", "N215", "N214", "N213", "N179", "N16", "N180", "N434", "N433", "N432", "N2", "N230", "N229", "N19", "N233", "N232", "N231", "N23", "N238", "N237", "N236", "N235", "N234", "N178", "N3", "N4", "N5", "N6", "N7", "N8"]}, {"Start Node": "N38", "End Node": "E1", "Start Time": "0:0:30", "Weight Class": "2", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N292", "N193", "N440", "N106", "N294", "N293", "N97", "N295", "N46", "N65", "N268", "N96", "N269", "N76", "N270", "N95", "N271", "N94", "N272", "N93", "N328", "N329", "E1"]}, {"Start Node": "A11", "End Node": "N8", "Start Time": "0:0:50", "Weight Class": "2", "route": ["A11", "N406", "N161", "N205", "N144", "N206", "N143", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N210", "N135", "N211", "N133", "N212", "N131", "N184", "N228", "N227", "N226", "N225", "N224", "N223", "N222", "N221", "N220", "N219", "N218", "N217", "N216", "N215", "N214", "N213", "N179", "N16", "N180", "N434", "N433", "N432", "N2", "N230", "N229", "N19", "N233", "N232", "N231", "N23", "N238", "N237", "N236", "N235", "N234", "N178", "N3", "N4", "N5", "N6", "N7", "N8"]}, {"Start Node": "N38", "End Node": "A17", "Start Time": "0:1:10", "Weight Class": "3", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N210", "N135", "N211", "N133", "N382", "N383", "A17"]}, {"Start Node": "N38", "End Node": "W2", "Start Time": "0:13:00", "Weight Class": "3", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N143", "N206", "N144", "N205", "N161", "N163", "N165", "N204", "N147", "N185", "N398", "N397", "N186", "N146", "N154", "N402", "W2"]}, {"Start Node": "N38", "End Node": "A12", "Start Time": "18:56:00", "Weight Class": "2", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N143", "N392", "N393", "A12"]}, {"Start Node": "N38", "End Node": "E20", "Start Time": "19:2:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N254", "N114", "N255", "N113", "N256", "N112", "N360", "N361", "E20"]}, {"Start Node": "N38", "End Node": "E1", "Start Time": "19:5:00", "Weight Class": "2", "route": ["N38", "N379", "N378", "N45", "N265", "N264", "N263", "N262", "N261", "N192", "N439", "N46", "N65", "N268", "N96", "N269", "N76", "N270", "N95", "N271", "N94", "N272", "N93", "N328", "N329", "E1"]}, {"Start Node": "N38", "End Node": "A15", "Start Time": "19:8:00", "Weight Class": "3", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N386", "N387", "A15"]}, {"Start Node": "N38", "End Node": "E22", "Start Time": "19:11:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N254", "N114", "N255", "N113", "N256", "N112", "N257", "N111", "N258", "N110", "N356", "N357", "E22"]}, {"Start Node": "N38", "End Node": "W1", "Start Time": "19:15:00", "Weight Class": "2", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N143", "N206", "N144", "N196", "N396", "N395", "N394", "N187", "N145", "N156", "N150", "N158", "N152", "N154", "N146", "N403", "W1"]}, {"Start Node": "N38", "End Node": "A11", "Start Time": "19:23:00", "Weight Class": "3", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N143", "N206", "N144", "N205", "N161", "N406", "A11"]}, {"Start Node": "N38", "End Node": "E25", "Start Time": "19:25:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N254", "N114", "N255", "N113", "N256", "N112", "N257", "N111", "N258", "N110", "N259", "N109", "N430", "N108", "N289", "N66", "N368", "N121", "N119", "N369", "E25"]}, {"Start Node": "A12", "End Node": "N8", "Start Time": "19:26:00", "Weight Class": "2", "route": ["A12", "N393", "N392", "N143", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N210", "N135", "N211", "N133", "N212", "N131", "N184", "N228", "N227", "N226", "N225", "N224", "N223", "N222", "N221", "N220", "N219", "N218", "N217", "N216", "N215", "N214", "N213", "N179", "N16", "N180", "N434", "N433", "N432", "N2", "N230", "N229", "N19", "N233", "N232", "N231", "N23", "N238", "N237", "N236", "N235", "N234", "N178", "N3", "N4", "N5", "N6", "N7", "N8"]}, {"Start Node": "N38", "End Node": "W6", "Start Time": "19:29:00", "Weight Class": "2", "route": ["N38", "N250", "N37", "N248", "N249", "N36", "N246", "N247", "N35", "N245", "N34", "N143", "N206", "N144", "N205", "N161", "N163", "N165", "N204", "N147", "N185", "N398", "N397", "N186", "N146", "N154", "N152", "N158", "N150", "N156", "N404", "W6"]}, {"Start Node": "N38", "End Node": "E23", "Start Time": "19:33:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N265", "N264", "N263", "N262", "N261", "N192", "N439", "N46", "N295", "N97", "N293", "N294", "N106", "N440", "N193", "N292", "N71", "N254", "N114", "N255", "N113", "N256", "N112", "N257", "N111", "N258", "N110", "N259", "N109", "N354", "N355", "E23"]}, {"Start Node": "N38", "End Node": "E12", "Start Time": "19:36:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N265", "N264", "N263", "N262", "N261", "N192", "N439", "N46", "N65", "N291", "N100", "N92", "N90", "N340", "N341", "E12"]}, {"Start Node": "N38", "End Node": "E6", "Start Time": "19:38:37", "Weight Class": "2", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N292", "N193", "N440", "N106", "N294", "N293", "N97", "N295", "N46", "N65", "N291", "N100", "N92", "N90", "N290", "N101", "N81", "N280", "N88", "N326", "N327", "E6"]}, {"Start Node": "N38", "End Node": "E6", "Start Time": "19:39:46", "Weight Class": "2", "route": ["N38", "N379", "N378", "N45", "N265", "N264", "N263", "N262", "N261", "N192", "N439", "N46", "N65", "N291", "N100", "N92", "N90", "N290", "N101", "N81", "N280", "N88", "N326", "N327", "E6"]}, {"Start Node": "A11", "End Node": "N8", "Start Time": "19:41:00", "Weight Class": "2", "route": ["A11", "N406", "N161", "N205", "N144", "N206", "N143", "N34", "N207", "N141", "N208", "N139", "N209", "N137", "N210", "N135", "N211", "N133", "N212", "N131", "N184", "N228", "N227", "N226", "N225", "N224", "N223", "N222", "N221", "N220", "N219", "N218", "N217", "N216", "N215", "N214", "N213", "N179", "N16", "N180", "N434", "N433", "N432", "N2", "N230", "N229", "N19", "N233", "N232", "N231", "N23", "N238", "N237", "N236", "N235", "N234", "N178", "N3", "N4", "N5", "N6", "N7", "N8"]}, {"Start Node": "N38", "End Node": "E4", "Start Time": "19:42:00", "Weight Class": "3", "route": ["N38", "N379", "N378", "N45", "N252", "N251", "N74", "N253", "N71", "N292", "N193", "N440", "N106", "N294", "N293", "N97", "N295", "N46", "N65", "N268", "N96", "N269", "N76", "N334", "N335", "E4"]}]